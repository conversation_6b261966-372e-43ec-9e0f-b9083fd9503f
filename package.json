{"name": "my-v0-project", "version": "0.1.0", "private": true, "type": "module", "sideEffects": false, "packageManager": "pnpm@10.17.1", "engines": {"node": ">=22", "pnpm": ">=10", "npm": "please-use-pnpm", "yarn": "please-use-pnpm"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "prod": "next build && next start", "analyze": "ANALYZE=true next build", "start": "next start", "typecheck": "tsc --noEmit", "lint": "next lint --fix", "validate": "pnpm typecheck && pnpm lint"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@shopify/hydrogen-react": "^2025.7.0", "@shopify/storefront-api-client": "^1.0.9", "@vercel/analytics": "1.3.1", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "dotenv": "^17.2.3", "embla-carousel-react": "8.5.1", "geist": "^1.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "^15.5.4", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "9.8.0", "react-dom": "^18.3.1", "react-hook-form": "^7.63.0", "react-resizable-panels": "^2.1.9", "recharts": "2.15.4", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "zod": "^3.25.76"}, "devDependencies": {"@eslint/js": "^9.36.0", "@next/bundle-analyzer": "^15.5.4", "@next/eslint-plugin-next": "^15.5.4", "@stylistic/eslint-plugin": "^5.4.0", "@tailwindcss/postcss": "^4.1.14", "@types/node": "^22.18.8", "@types/react": "^18.3.25", "@types/react-dom": "^18.3.7", "eslint": "^9.36.0", "eslint-config-next": "15.5.4", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^6.1.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.14", "tw-animate-css": "1.3.3", "typescript": "^5.9.3", "typescript-eslint": "^8.45.0"}, "browserslist": ["Chrome >= 109", "Firefox >= 109", "Safari >= 16.4", "Edge >= 109"]}