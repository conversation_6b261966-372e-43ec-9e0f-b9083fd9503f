'use client';

import {Money} from '@shopify/hydrogen-react';
import {CurrencyCode} from '@shopify/hydrogen-react/storefront-api-types';
import {Heart, ShoppingCart} from 'lucide-react';
import Image from 'next/image';
import {useState} from 'react';

// Custom product type that matches our transformed data structure
type TransformedProduct = {
  id: string;
  shopifyId: string;
  name: string;
  category: string;
  price: number;
  image: string;
  limited: boolean;
  favorited: boolean;
  handle: string;
  description: string;
  tags: string[];
  productType: string;
  vendor: string;
  variants: {
    edges?: {
      node: {
        id: string;
        availableForSale: boolean;
      };
    }[];
  };
  priceRange: {
    minVariantPrice: {
      amount: string;
      currencyCode: CurrencyCode;
    };
  };
};

type ProductGridClientProps = {
  products: TransformedProduct[];
  categories: string[];
};

export default function ProductGridClient({products, categories}: ProductGridClientProps) {
  const [activeCategory, setActiveCategory] = useState('all');
  const [favorites, setFavorites] = useState<string[]>([]);

  const filteredProducts
    = activeCategory === 'all'
      ? products
      : products.filter((product) => product.category === activeCategory);

  const toggleFavorite = (productId: string) => {
    setFavorites((prev) =>
      prev.includes(productId)
        ? prev.filter((id) => id !== productId)
        : [...prev, productId],
    );
  };

  const handleAddToCart = async (product: TransformedProduct) => {
    console.log('Adding to cart:', product.name);

    // If we have variant data, log the variant ID for future cart implementation
    if (product.variants?.edges?.[0]?.node) {
      console.log('Variant ID:', product.variants.edges[0].node.id);
      console.log('Available for sale:', product.variants.edges[0].node.availableForSale);
    }

    // TODO: Implement actual cart functionality using Shopify cart mutations
  };

  return (
    <section className="relative z-10 px-4 py-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8 flex justify-center">
          <div className="browser-window">
            <div className="browser-content p-4">
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => setActiveCategory(category)}
                    className={`px-4 py-2 font-medium transition-colors border-2 ${
                      activeCategory === category
                        ? 'bg-purple-600 text-white border-gray-600'
                        : 'bg-purple-800 text-purple-200 hover:bg-purple-700 border-gray-500'
                    }`}
                    style={{borderRadius: 0}}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => (
            <div key={product.id} className="bg-gray-300 border-2 border-gray-600" style={{borderRadius: 0}}>
              {/* Window Title Bar */}
              <div className="bg-pink-400 border-b-2 border-gray-600 px-3 py-2 flex items-center justify-between">
                <h3 className="text-black font-bold text-sm truncate">{product.name}</h3>
                <div className="flex items-center gap-1">
                  <div className="w-4 h-4 bg-gray-500 border border-gray-700"></div>
                  <div className="w-4 h-4 bg-gray-500 border border-gray-700"></div>
                  <div className="w-4 h-4 bg-red-500 border border-gray-700 flex items-center justify-center">
                    <span className="text-white text-xs font-bold">×</span>
                  </div>
                </div>
              </div>

              {/* Window Content */}
              <div className="bg-pink-200 p-4 relative">
                {/* LIMITED badge and heart icon */}
                <div className="flex items-start justify-between mb-4">
                  {product.limited && (
                    <span className="bg-purple-600 text-white text-xs px-2 py-1 font-bold border-2 border-gray-600">
                      LIMITED
                    </span>
                  )}
                  <button
                    onClick={() => toggleFavorite(product.id)}
                    className="bg-gray-300 border-2 border-gray-600 p-1 ml-auto"
                  >
                    <Heart
                      className={`w-4 h-4 ${favorites.includes(product.id) ? 'fill-pink-500 text-pink-500' : 'text-gray-600'}`}
                    />
                  </button>
                </div>

                {/* Product Image Area */}
                <div className="bg-pink-100 border-2 border-gray-600 aspect-square mb-4 flex items-center justify-center relative">
                  <Image
                    src={product.image || '/placeholder.svg'}
                    alt={product.name}
                    width={800}
                    height={800}
                    className="w-full h-full object-cover"
                  />
                  {/* Decorative purple circle */}
                  <div className="absolute top-4 right-4 w-8 h-8 bg-purple-400 rounded-full opacity-80"></div>
                  <button className="absolute inset-0 bg-black/0 hover:bg-black/10 transition-colors flex items-center justify-center">
                    <span className="bg-gray-400 text-black px-3 py-1 font-bold opacity-0 hover:opacity-100 transition-opacity border-2 border-gray-600">
                      quick view
                    </span>
                  </button>
                </div>

                {/* Category Input Box */}
                <div className="mb-4">
                  <input
                    type="text"
                    value={product.category?.toUpperCase() || 'UNCATEGORIZED'}
                    readOnly
                    className="w-full bg-white border-2 border-gray-600 px-2 py-1 text-purple-600 font-bold text-sm"
                    style={{borderRadius: 0}}
                  />
                </div>

                {/* Price and Add Button */}
                <div className="flex items-center justify-between gap-4">
                  <div className="bg-white border-2 border-gray-600 px-3 py-2">
                    {product.priceRange?.minVariantPrice
                      ? (
                          <Money
                            data={product.priceRange.minVariantPrice}
                            className="text-purple-600 font-bold text-lg"
                          />
                        )
                      : (
                          <span className="text-purple-600 font-bold text-lg">
                            $
                            {product.price.toFixed(2)}
                          </span>
                        )}
                  </div>

                  {/* Add to Cart Button */}
                  <button
                    onClick={() => handleAddToCart(product)}
                    className="bg-pink-500 text-white px-4 py-2 font-bold hover:bg-pink-600 transition-colors flex items-center gap-2 border-2 border-gray-600"
                    disabled={
                      product.variants?.edges?.[0]?.node
                      && !product.variants.edges[0].node.availableForSale
                    }
                  >
                    <ShoppingCart className="w-4 h-4" />
                    {product.variants?.edges?.[0]?.node
                      && !product.variants.edges[0].node.availableForSale
                      ? 'sold out'
                      : 'add'}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredProducts.length === 0 && activeCategory !== 'all' && (
          <div className="text-center mt-8">
            <div className="browser-window">
              <div className="browser-content p-8">
                <h3 className="text-xl font-bold text-purple-600 mb-2">No products in this category</h3>
                <p className="text-gray-700">Try selecting a different category or check back later!</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
