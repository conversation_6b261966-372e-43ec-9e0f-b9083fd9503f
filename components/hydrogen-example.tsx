'use client';

import {
  AddToCartButton,
  Money,
  ProductProvider,
  ShopPayButton,
  useCart,
} from '@shopify/hydrogen-react';

// Example component showing how to use Hydrogen React components
export function HydrogenExample({product}: {product: unknown}) {
  const {totalQuantity} = useCart();

  if (!product) {
    return <div>No product data</div>;
  }

  const productWithVariants = product as {
    variants?: {
      edges?: {
        node: {
          id: string;
          price: {amount: string; currencyCode: string};
          availableForSale: boolean;
        };
      }[];
    };
  };
  const firstVariant = productWithVariants?.variants?.edges?.[0]?.node;

  return (
    <ProductProvider data={product}>
      <div className="space-y-4 p-4 border border-gray-300 rounded">
        <h3 className="text-lg font-bold">Hydrogen React Components Example</h3>

        {/* Product Price using Money component */}
        <div>
          <span className="text-sm text-gray-600">Price: </span>
          {firstVariant?.price && (
            <span className="font-bold text-purple-600">
              $
              {firstVariant.price.amount}
              {' '}
              {firstVariant.price.currencyCode}
            </span>
          )}
        </div>

        {/* Add to Cart Button */}
        {firstVariant && (
          <AddToCartButton
            variantId={firstVariant.id}
            quantity={1}
            className="bg-pink-500 text-white px-4 py-2 font-bold hover:bg-pink-600 transition-colors border-2 border-gray-600"
            disabled={!firstVariant.availableForSale}
          >
            {firstVariant.availableForSale ? 'Add to Cart' : 'Sold Out'}
          </AddToCartButton>
        )}

        {/* Shop Pay Button */}
        {firstVariant && (
          <ShopPayButton
            variantIds={[firstVariant.id]}
            className="w-full"
          />
        )}

        {/* Cart Info */}
        <div className="text-sm text-gray-600">
          Cart:
          {' '}
          {totalQuantity}
          {' '}
          items
        </div>
      </div>
    </ProductProvider>
  );
}

// Example of using cart hooks
export function CartSummary() {
  const {lines, cost, totalQuantity} = useCart();

  return (
    <div className="p-4 bg-gray-100 rounded">
      <h4 className="font-bold mb-2">Cart Summary</h4>
      <p>
        Items:
        {totalQuantity}
      </p>
      {cost?.totalAmount && (
        <p>
          Total:
          {' '}
          <Money data={cost.totalAmount} />
        </p>
      )}

      {lines && lines.length > 0 && (
        <div className="mt-2">
          <h5 className="font-semibold">Items:</h5>
          {lines.map((line: {
            id: string;
            quantity: number;
            merchandise: {
              product: {
                title: string;
              };
            };
          }) => (
            <div key={line.id} className="text-sm">
              {line.merchandise.product.title}
              {' '}
              x
              {line.quantity}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
