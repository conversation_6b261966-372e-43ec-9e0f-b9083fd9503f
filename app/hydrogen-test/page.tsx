import {getProducts} from '@/lib/shopify';
import {HydrogenExample, CartSummary} from '@/components/hydrogen-example';

export default async function HydrogenTestPage() {
  try {
    const productsData = await getProducts(1);
    const product = productsData?.edges?.[0]?.node;

    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-pink-900 to-purple-900 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white border-4 border-gray-600 p-6 mb-8">
            <h1 className="text-3xl font-bold text-purple-600 mb-4">
              Hydrogen React Components Test
            </h1>
            <p className="text-gray-700 mb-6">
              This page demonstrates Hydrogen React components working with your Shopify store.
            </p>

            {product
              ? (
                  <div className="space-y-8">
                    <div>
                      <h2 className="text-xl font-bold mb-4">
                        Product:
                        {product.title}
                      </h2>
                      <HydrogenExample product={product} />
                    </div>

                    <div>
                      <h2 className="text-xl font-bold mb-4">Cart Status</h2>
                      <CartSummary />
                    </div>

                    <div className="bg-gray-100 p-4 rounded">
                      <h3 className="font-bold mb-2">Available Hydrogen React Components:</h3>
                      <ul className="list-disc list-inside space-y-1 text-sm">
                        <li>
                          <code>AddToCartButton</code>
                          {' '}
                          - Adds products to cart
                        </li>
                        <li>
                          <code>Money</code>
                          {' '}
                          - Formats currency display
                        </li>
                        <li>
                          <code>ProductProvider</code>
                          {' '}
                          - Provides product context
                        </li>
                        <li>
                          <code>CartProvider</code>
                          {' '}
                          - Provides cart context
                        </li>
                        <li>
                          <code>ShopPayButton</code>
                          {' '}
                          - Shop Pay checkout button
                        </li>
                        <li>
                          <code>useCart</code>
                          {' '}
                          - Hook for cart state
                        </li>
                        <li>
                          <code>useProduct</code>
                          {' '}
                          - Hook for product context
                        </li>
                        <li>
                          <code>useMoney</code>
                          {' '}
                          - Hook for currency formatting
                        </li>
                      </ul>
                    </div>
                  </div>
                )
              : (
                  <div className="text-center py-8">
                    <p className="text-gray-600">No products found in your Shopify store.</p>
                    <p className="text-sm text-gray-500 mt-2">
                      Add some products to your Shopify admin to see them here.
                    </p>
                  </div>
                )}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading products:', error);
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-pink-900 to-purple-900 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white border-4 border-gray-600 p-6">
            <h1 className="text-3xl font-bold text-red-600 mb-4">Error</h1>
            <p className="text-gray-700">
              Failed to load products. Check your Shopify configuration.
            </p>
          </div>
        </div>
      </div>
    );
  }
}
